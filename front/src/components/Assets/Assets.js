import React, { Component } from 'react'
import { connect } from 'react-redux'
import * as actions from '../../actions/fetchActions'
import * as Cookies from 'src/Cookies'
import { CardAsset, CardAssetDownload } from './'

import _ from 'lodash'
import NoResultsFound from 'src/v2/pages/MaterialsLists/components/NoResultsFound'
import ModalDownloadAsset from '../Modals/ModalDownloadAsset'
import ModalConfirmDownload from '../Modals/ModalConfirmDownload'
import { withRouter } from 'react-router-dom'
import { DisplayingResult } from './../utils'
import ModalConfirm from '../utils/ModalConfirm'
import { PropTypes } from 'prop-types'
import { findElementPos } from '../../utils'
import { Button, Icon, Title, RsLoader } from '../../components'
import { getS3Zip, handlerToControlDownloadProcess } from '../../redux/DownloadQueue/handlers'
import { addDownloadElementToQueue } from '../../redux/DownloadQueue/actionsCreators'

import AdobeAnalytics from 'src/v2/intances/AdobeAnalytics'

const { eventDispatch } = AdobeAnalytics
class Assets extends Component {
  constructor(props) {
    super(props)
    this.state = {
      // asset: this.props.asset,
      displayedAssets: [],
      pageIndex: 0,
      pagination: 6,
      displayModalDownload: false,
      displayModalConfirmDownload: false,
      selectedAssetData: [],
      isBulkActionActive: false,
      bulkDownloadAssets: [],
      confirmedDownloadAssets: {},
      previewModal: false,
      displaySection: true,
      fetchingAssetInfo: false,
      onlyVideos: false,
      downloadStatus: null,
      status: null,
      dispatchDownload: false,
      apiCallCount: 0,
      apiError: false
    }

    this.openConfirmModal = this.openConfirmModal.bind(this)
    this.closeConfirmModal = this.closeConfirmModal.bind(this)
  }

  componentDidMount() {
    let currentUrlParams = new URLSearchParams(window.location.search)
    let currentPage = currentUrlParams.get('assetPage')
    if (!currentPage) {
      currentPage = 0
    }
    if (currentPage) {
      this.setState({ pageIndex: currentPage * 1 })
    }
    if (this.props.pagination && this.props.assets) {
      let rpp = parseInt(this.props.pagination)
      this.setState({ pagination: rpp })
      this.loadPaginator(this.props.assets, rpp, currentPage)
    } else if (this.props.assets) {
      this.loadPaginator(this.props.assets, this.state.pagination, currentPage)
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.setState({ displayModalConfirmDownload: false })

    if (this.state.apiError) return

    if (nextProps.pagination && nextProps.assets) {
      let pagination = parseInt(nextProps.pagination)

      if (nextProps.pagination !== this.props.pagination) {
        let currentUrlParams = new URLSearchParams(window.location.search)
        currentUrlParams.set('assetPage', 0)

        this.props.savePage &&
          this.props.history.replace(
            window.location.pathname + '?' + decodeURIComponent(currentUrlParams)
          )

        this.setState({ pageIndex: 0, pagination })
        this.loadPaginator(nextProps.assets, pagination, 0)
      } else {
        let currentUrlParams = new URLSearchParams(window.location.search)
        let currentPage = currentUrlParams.get('assetPage')

        if (this.props.isDownloads) {
          this.setState({ pageIndex: currentPage * 1, pagination: 12 })
          this.loadPaginator(nextProps.assets, 12, currentPage, '4')
        } else {
          if (nextProps.assets !== this.props.assets) {
            this.setState({ pageIndex: currentPage * 1, pagination })
            this.loadPaginator(nextProps.assets, pagination, currentPage * 1)
          } else {
            this.setState({ pageIndex: currentPage * 1, pagination })
            this.loadPaginator(nextProps.assets, pagination, currentPage * 1)
          }
        }
      }
    } else if (nextProps.assets) {
      this.loadPaginator(nextProps.assets, this.state.pagination)
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { confirmedDownloadAssets } = this.state

    if (!_.isEmpty(confirmedDownloadAssets) && this.state.dispatchDownload) {
      this.setState({ dispatchDownload: false })
      this.props.handlerToControlDownloadProcess({
        confirmedDownloadAssets,
        setState: this.setState.bind(this)
      })
    }

    if (prevProps.assetsInfo.length !== this.props.assetsInfo.length) {
      this.loadPaginator(this.props.assets, this.state.pagination)
    }
    console.log('api error', this.state.apiError)
  }

  loadPaginator(_assets, _pagination, _pageIndex) {
    if (typeof _pageIndex === 'undefined') {
      _pageIndex = this.state.pageIndex
    }
    let _displayedAssets = []
    if (_pagination <= _assets.length) {
      _displayedAssets = [..._assets].slice(
        _pageIndex * _pagination,
        _pageIndex * _pagination + _pagination
      )
    } else {
      _displayedAssets = _assets
    }

    if (!this.props.isDownloads) {
      let notExists = _displayedAssets.filter((asset) => {
        return typeof this.props.assetsInfo[asset.id] === 'undefined'
      })

      if (notExists.length > 0) {
        if (this.state.apiCallCount < 5) {
          this.setState((prevState) => ({
            fetchingAssetInfo: true,
            apiCallCount: prevState.apiCallCount + 1
          }))
          this.props.fetchAssetInfo(notExists)
          console.log('fetch asset info', this.props.fetchAssetInfo(notExists));
        } else {
          this.setState({
            fetchingAssetInfo: false,
            apiError: true
          })
        }
      } else {
        this.setState({
          fetchingAssetInfo: false,
          displayedAssets: _displayedAssets
        })
        if (_displayedAssets.length === 0 && this.state.pageIndex)
          this.reloadPaginator(this.state.pageIndex - 1)
      }
    } else {
      this.setState({
        fetchingAssetInfo: false,
        displayedAssets: _displayedAssets
      })
    }
  }
  reloadPaginator(pIndex) {
    let currentUrlParams = new URLSearchParams(window.location.search)
    currentUrlParams.set('assetPage', pIndex)
    const cookies = Cookies.getCookies()
    const privateToken = cookies['unicorn-access-token']

    this.props.savePage &&
      this.props.history.replace(
        window.location.pathname + '?' + decodeURIComponent(currentUrlParams)
      )

    if (this.props.isDownloads) {
      const label = privateToken
        ? 'myDownloads_pagination_' + (pIndex + 1)
        : 'guest_myDownloads_pagination_' + (pIndex + 1)
      eventDispatch({ action: 'click', category: 'resources_shopper_page', label })
    } else {
      let label = privateToken
        ? 'library_pagination_' + (pIndex + 1)
        : 'guest_library_pagination_' + (pIndex + 1)
      if (this.props.paginationEvent) {
        label = this.props.paginationEvent + (pIndex + 1)
      }
      eventDispatch({
        action: 'click',
        category: 'resources_shopper_page',
        label
      })
    }

    this.setState({ pageIndex: pIndex })
    this.loadPaginator(this.props.assets, this.state.pagination, pIndex)

    findElementPos('assets')
  }

  openModal(assetData) {
    this.setState({ displayModalDownload: true, selectedAssetData: assetData })
  }

  openConfirmModalDownload(data) {
    this.setState({
      confirmedDownloadAssets: [
        { ...data[0], thumbnail_url: data[0].description[0].thumbnail_url }
      ],
      dispatchDownload: true
    })
  }

  closeModal(data) {
    if (data) {
      this.setState({
        displayModalDownload: false,
        displayModalConfirmDownload: false,
        confirmedDownloadAssets: data,
        selectedAssetData: [],
        dispatchDownload: true
      })
    } else {
      this.setState({
        displayModalDownload: false,
        selectedAssetData: [],
        displayModalConfirmDownload: false
      })
    }
  }

  closeModalConfirm(bool) {
    this.setState({ displayModalConfirmDownload: false })
    if (bool) {
      if (!this.props.isDownloads) {
        this.props.history.replace('/myAccount/downloads')
      }
    }
  }

  openBulkActions() {
    this.setState({ isBulkActionActive: true })
  }

  closeBulkActions() {
    this.setState({ isBulkActionActive: false, bulkDownloadAssets: [] })
  }

  downloadBulkAssets() {
    let selectedMaterialsData = []
    let newArray = []
    ;[...this.props.assetsInfo].map((el) => {
      if (el) {
        newArray.push(el)
      }
    })
    ;[...this.state.bulkDownloadAssets].forEach((el, index) => {
      let assetData = newArray.filter((asset) => asset.asset === el)
      selectedMaterialsData.push(...assetData)
    })

    this.openModal(selectedMaterialsData)
  }

  addToBulkDownloads(assetId) {
    if (this.state.bulkDownloadAssets.includes(assetId)) {
      let reducedArr = [...this.state.bulkDownloadAssets].filter((el) => el !== assetId)
      this.setState({ bulkDownloadAssets: reducedArr })
    } else {
      this.setState({
        bulkDownloadAssets: [...this.state.bulkDownloadAssets, assetId]
      })
    }
  }

  removeBulkAssets() {
    this.setState({
      previewModal: true
    })
  }

  renderList(assets) {
    let styleList
    if (this.props.layout === 1) {
      styleList = 'elements elements-result display-row'
    } else {
      styleList = 'elements elements-result display-column'
    }
    if (this.props.isFavourites) {
      styleList = 'elements elements-result'
    }
    if (this.props.isDownloads) {
      styleList = 'elements elements-result display-row'
    }

    return (
      <>
        {this.state.displaySection ? (
          <div className={`${styleList}`}>
            {this.state.displayModalDownload && (
              <ModalDownloadAsset
                onClose={(data) => this.closeModal(data)}
                materialData={this.state.selectedAssetData}
                show={this.state.displayModalDownload}
                languageId={this.props.languageId}
                downloadEvent={this.props.downloadBulkEvent}
              />
            )}
            {this.state.displayModalConfirmDownload && (
              <ModalConfirmDownload
                onClose={(data) => this.closeModalConfirm(data)}
                data={this.state.confirmedDownloadAssets}
                show={this.state.displayModalConfirmDownload}
              />
            )}

            {this.props.isDownloads &&
              assets.map((asset, index) => {
                let descriptionLanguageDwn =
                  asset.description.filter((l) => l.language_id === this.props.languageId)[0] ||
                  asset.description.filter((l) => l.language_id === 1)[0] ||
                  asset.description[0]

                let audienceLanguageDwn =
                  asset.audience.filter((a) => a.language_id === this.props.languageId).length > 0
                    ? asset.audience.filter((a) => a.language_id === this.props.languageId)
                    : asset.audience.filter((a) => a.language_id === 1)

                let assetTypesLanguageDwn =
                  asset.asset_types.filter((a) => a.language_id === this.props.languageId).length >
                  0
                    ? asset.asset_types.filter((a) => a.language_id === this.props.languageId)
                    : asset.asset_types.filter((a) => a.language_id === 1)
                return (
                  <div className="container-card-download" key={index}>
                    <CardAssetDownload
                      languageId={this.props.languageId}
                      openConfirmModalDownload={(data) => this.openConfirmModalDownload([data])}
                      asset={asset}
                      indexCard={index}
                      description={descriptionLanguageDwn}
                      audience={audienceLanguageDwn}
                      assetType={assetTypesLanguageDwn}
                      customizableData={asset.json}
                    />
                  </div>
                )
              })}

            {!this.props.isDownloads &&
              assets.map((asset, index) => {
                let p = this.props.assetsInfo[asset.id]

                if (this.props.isFavourites && p.bookmark === 0) {
                  return null
                } else {
                  let descriptionLanguage =
                    p.description.filter((l) => l.language_id === this.props.languageId)[0] ||
                    p.description.filter((l) => l.language_id === 1)[0] ||
                    p.description[0]
                  let audienceLanguage =
                    p.audience.filter((a) => a.language_id === this.props.languageId).length > 0
                      ? p.audience.filter((a) => a.language_id === this.props.languageId)
                      : p.audience.filter((a) => a.language_id === 1)

                  return (
                    <div className="container-card" key={index}>
                      <CardAsset
                        languageId={this.props.languageId}
                        isBulkActionActive={this.state.isBulkActionActive}
                        bulkDownloadAssets={this.state.bulkDownloadAssets}
                        addToBulkDownloads={(id) => this.addToBulkDownloads(id)}
                        openModal={(data) => this.openModal(data)}
                        key={p.id}
                        asset={p}
                        indexCard={index}
                        description={descriptionLanguage}
                        audience={audienceLanguage}
                        featured={p.featured}
                        isFavourites={this.props.isFavourites}
                        isDownloads={this.props.isDownloads}
                        bookmarkEvent={this.props.bookmarkEvent}
                        confirmRemoveEvent={this.props.confirmRemoveEvent}
                        detailsEvent={this.props.detailsEvent}
                        downloadEvent={this.props.downloadEvent}
                        stepOnboardingLang={index === 0 ? this.props.stepOnboardingLang : -1}
                        onCloseOnboarding={this.props.onCloseOnboarding}
                        onGoToStep={this.props.onGoToStep}
                      />
                    </div>
                  )
                  // }
                }
              })}
          </div>
        ) : (
          ''
        )}
      </>
    )
  }
  renderPagination() {
    let amountOfPages = Math.ceil(this.props.assets.length / this.state.pagination)
    if (amountOfPages > 1) {
      //let arr = [...Array(amountOfPages).keys()].map(x => ++x);

      let arr = [1, 2, 3, 4, 5]

      let _pageIndex = this.state.pageIndex + 1

      if (_pageIndex > 4) {
        arr = [_pageIndex - 2, _pageIndex - 1, _pageIndex, _pageIndex + 1, _pageIndex + 2]
      }

      if (_pageIndex > amountOfPages - 2) {
        arr = [
          amountOfPages - 4,
          amountOfPages - 3,
          amountOfPages - 2,
          amountOfPages - 1,
          amountOfPages
        ]
      }

      return (
        <ul className="rs-pagination pag-numbers">
          {_pageIndex > 1 ? (
            <li
              key={'prev'}
              onClick={() => this.reloadPaginator(_pageIndex - 2)}
              className="previous"
            >
              <Icon iconName="arrow" direction="left" color="blue-energy-intel" />
            </li>
          ) : (
            ''
          )}
          {amountOfPages !== 5 && _pageIndex > 4 ? (
            <>
              <li key={'first'} onClick={() => this.reloadPaginator(0)} className="first">
                1
              </li>
              {2 < arr[0] && <li className="ellipsis">...</li>}
            </>
          ) : (
            ''
          )}
          {arr.map((el) => {
            if (el <= amountOfPages && el > 0) {
              if (el === _pageIndex) {
                return (
                  <li key={el} className="active" onClick={() => this.reloadPaginator(el - 1)}>
                    {el}
                  </li>
                )
              }
              return (
                <li key={el} onClick={() => this.reloadPaginator(el - 1)}>
                  {el}
                </li>
              )
            } else {
              return false
            }
          })}
          {arr[4] <= amountOfPages - 1 ? (
            <>
              {amountOfPages - 1 > arr[4] && <li className="ellipsis">...</li>}
              <li
                key={'last'}
                onClick={() => this.reloadPaginator(amountOfPages - 1)}
                className="last"
              >
                {amountOfPages}
              </li>
            </>
          ) : (
            ''
          )}
          {_pageIndex < amountOfPages ? (
            <li key={'next'} onClick={() => this.reloadPaginator(_pageIndex)} className="next">
              <Icon iconName="arrow" direction="right" color="blue-energy-intel" />
            </li>
          ) : (
            ''
          )}
        </ul>
      )
    }
  }

  openConfirmModal() {
    this.setState({ previewModal: false }, () => {
      for (let i = 0; i < this.state.bulkDownloadAssets.length; i++) {
        this.props.removeBookmark(this.state.bulkDownloadAssets[i], 1, this.props.isFavourites)
      }
      this.closeBulkActions()
      this.props.onConfirmCloseModal()
    })
  }

  closeConfirmModal() {
    this.setState({ previewModal: false })
  }

  handleClick(status) {
    this.setState({ displaySection: !status })
  }

  render() {
    const { isHome, visible, title, isNoMatchesData, onConfirmModal, onConfirmCloseModal } =
      this.props

    const { previewModal } = this.state
    const cookies = Cookies.getCookies()
    const privateToken = cookies['unicorn-access-token']
    if (!this.props.assets || !this.props.assetsInfo || this.state.fetchingAssetInfo) {
      return <RsLoader />
    }
    return (
      <>
        {this.props.assets.length > 0 ? (
          <div
            className={`elements-wrapper ${this.state.isBulkActionActive && `disable-card-click`}`}
            id="assets"
          >
            <ModalConfirm
              modal={previewModal}
              // title={""}
              icon={this.props.isFavourites ? 'icon-ctm icon-delete' : 'icon-ctm icon-red-cross'}
              description={this.context.t('assetsPage').ModalConfirmDownload.description}
              onYes={this.openConfirmModal}
              onNo={this.closeConfirmModal}
              context={this.context}
            />
            <div className="header-list">
              {!this.props.isDownloads && !isNoMatchesData ? (
                <div
                  className={visible !== true ? 'bulk-actions multiple-activated' : 'bulk-actions'}
                >
                  {this.state.isBulkActionActive ? (
                    <>
                      <Button
                        type="outline2"
                        customClass="button-done"
                        onClick={
                          !this.props.isFavourites
                            ? () => {
                                this.props.onClick()
                                this.closeBulkActions()
                              }
                            : () => {
                                this.closeBulkActions()
                                this.props.onDownloadMultipleDone()
                              }
                        }
                      >
                        {this.context.t('assetsPage').done}
                      </Button>
                      {this.props.isFavourites && this.state.bulkDownloadAssets.length > 0 ? (
                        <Button
                          type="delete"
                          customClass="button-trash"
                          icon={<Icon iconName="delete-active2" />}
                          onClick={() => {
                            this.removeBulkAssets()
                            this.props.onDownloadMultipleDone()
                            onConfirmModal()
                          }}
                        ></Button>
                      ) : null}

                      {this.state.bulkDownloadAssets.length > 0 ? (
                        <Button
                          customClass="download-counter-acived"
                          onClick={() => this.downloadBulkAssets()}
                        >
                          <span className="icon-download-white"></span>
                          {this.context.t('assetsPage').download}
                          <span className="dl-count">{this.state.bulkDownloadAssets.length}</span>
                        </Button>
                      ) : (
                        ''
                      )}
                    </>
                  ) : this.props.isFavourites ? (
                    <Button
                      customClass="button-multiple-favourites"
                      icon={<Icon iconName="rs-select-multiple" />}
                      onClick={() => {
                        const label = privateToken
                          ? 'myBookmarks_selectMultiple'
                          : 'guest_myBookmarks_selectMultiple'
                        eventDispatch({
                          action: 'click',
                          category: 'resources_shopper_page',
                          label
                        })
                        this.openBulkActions()
                        this.props.onDownloadMultiple()
                        onConfirmCloseModal()
                      }}
                    >
                      {this.context.t('assetsPage').selectMultiple}
                    </Button>
                  ) : (
                    <Button
                      onClick={() => {
                        let label = privateToken
                          ? 'library_download_multiple'
                          : 'guest_library_download_multiple'
                        if (this.props.multipleDownloadEvent) {
                          label = this.props.multipleDownloadEvent
                        }
                        eventDispatch({
                          action: 'click',
                          category: 'resources_shopper_page',
                          label
                        })
                        this.openBulkActions()
                        this.props.onClick()
                      }}
                      customClass="button-multiple"
                      icon={<Icon iconName="download" />}
                    >
                      {this.context.t('assetsPage').downloadMultiple}
                    </Button>
                  )}
                </div>
              ) : null}
              <div>{this.props.children}</div>
              {isHome && !isNoMatchesData && (
                <div className="title-box">
                  <Title text={title} size="36" type="h4" color="greyish-brown2" />
                  {!this.state.apiError && (
                    <Icon
                      iconName={this.state.displaySection ? 'minus-circle' : 'add-circle'}
                      onClick={() => {
                        this.handleClick(this.state.displaySection)
                      }}
                    />
                  )}
                </div>
              )}
              {isNoMatchesData && (
                <div className="no-match-title">
                  <h2>{title}</h2>
                </div>
              )}
            </div>

            {this.renderList(this.state.displayedAssets)}
            {this.state.displaySection ? ( // pagination falla porque this.state.apiError en ocasiones es true
              <div className="rs-pagination-wrapper">
                <DisplayingResult
                  state={this.state}
                  total={this.props.assets.length}
                  perPage={this.state.displayedAssets.length}
                  context={this.context}
                />
                {this.props.assets.length > 0 ? this.renderPagination() : null}
              </div>
            ) : this.state.apiError && !this.state.displaySection ? (
              <NoResultsFound />
            ) : null}
          </div>
        ) : this.props.isHome || this.props.isFavourites || this.props.isDownloads ? (
          ''
        ) : (
          <>
            <div>{this.props.children}</div>
            <NoResultsFound />
          </>
        )}
      </>
    )
  }
}

const mapStateToProps = (state, myProps) => {
  return {
    assets: myProps.assets ? myProps.assets : state.reducer.assets,
    assetsInfo: state.reducer.assetsInfo,
    pagination: myProps.pagination || state.reducer.preferences.pagination || 6,
    layout: state.reducer.preferences.layout,
    bookmarksAssets: state.reducer.bookmarksAsset,
    languageId: myProps.languageId ? myProps.languageId : state.reducer.preferences.ui_language_id,
    userId: state.reducer.preferences.id
  }
}

Assets.contextTypes = {
  t: PropTypes.func.isRequired
}

export default connect(mapStateToProps, {
  ...actions,
  addDownloadElementToQueue,
  getS3Zip,
  handlerToControlDownloadProcess
})(withRouter(Assets))
